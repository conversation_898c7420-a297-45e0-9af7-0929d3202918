import type React from "react"
import { useState, useMemo, useEffect } from "react"
import { GoogleMap, Marker, Circle, HeatmapLayer, useJsApiLoader } from "@react-google-maps/api"
import { Card } from "~/components/ui/card"
import { MapPin, Users } from "lucide-react"

interface Customer {
  id: string
  name: string
  lat: number
  lng: number
  orderCount?: number
  lastOrderDate?: string
}

interface Shop {
  id: string
  name: string
  lat: number
  lng: number
}

interface HeatMapProps {
  shop: Shop
  customers: Customer[]
  radiusKm: number
}

const GOOGLE_MAPS_API_KEY = "AIzaSyDBh6D6NIEiH08bj01ybByaayfM1T7W6XY"
const GOOGLE_MAPS_LIBRARIES: ("places" | "geometry" | "visualization")[] = ["places", "visualization"]

const HeatMap: React.FC<HeatMapProps> = ({ shop, customers, radiusKm }) => {
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    libraries: GOOGLE_MAPS_LIBRARIES,
  });

  useEffect(() => {
    console.log("isLoaded::", isLoaded)
    console.log("loadError::", loadError)
  }, [isLoaded, loadError]);

  const mapContainerStyle = {
    width: "100%",
    height: "600px",
    borderRadius: "12px",
  }

  const center = useMemo(
    () => ({
      lat: shop.lat,
      lng: shop.lng,
    }),
    [shop.lat, shop.lng],
  )

  if (!isLoaded) {
    return (
      <Card className="p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading Map...</p>
        </div>
      </Card>
    )
  }

  if (loadError) {
    return (
      <Card className="p-8">
        <div className="text-center text-red-500">
          <MapPin className="mx-auto mb-2 h-8 w-8" />
          <p>Error: {loadError.message}</p>
        </div>
      </Card>
    )
  }

  if (!shop || !shop.lat || !shop.lng) {
    return (
      <Card className="p-8">
        <div className="text-center text-red-500">
          <MapPin className="mx-auto mb-2 h-8 w-8" />
          <p>Error: Shop location data is missing</p>
        </div>
      </Card>
    )
  }

  if (!customers || customers.length === 0) {
    return (
      <Card className="p-8">
        <div className="text-center text-yellow-500">
          <Users className="mx-auto mb-2 h-8 w-8" />
          <p>No customer data available</p>
        </div>
      </Card>
    )
  }

  return (
    <div>

      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={center}
        zoom={15}
      >
        <Marker position={center} />}
      </GoogleMap>

    </div>
  )
}

export default HeatMap
